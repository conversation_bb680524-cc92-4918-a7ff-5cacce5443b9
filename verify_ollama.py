import requests
import json
import sys

def verify_ollama_service(host="0.0.0.0", port=9000):
    """
    Verify that the Ollama service is running and responding correctly
    """
    # Check if the service is accessible
    try:
        base_url = f"http://{host}:{port}"
        print(f"Checking Ollama service at {base_url}")
        
        # Test the API root endpoint
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        
        if response.status_code == 200:
            print("✅ Successfully connected to Ollama service")
            models = response.json()
            print(f"📦 Available models: {len(models.get('models', [])) if 'models' in models else 0}")
            
            # Check if qwen3-coder model is available
            model_found = False
            if 'models' in models:
                for model in models['models']:
                    if 'qwen3-coder' in model.get('name', ''):
                        print("🔍 Found qwen3-coder model")
                        model_found = True
                        break
            
            if not model_found:
                print("⚠️  qwen3-coder model not found in the list of available models")
                
            return True
        else:
            print(f"❌ Unexpected response status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Failed to connect to Ollama service at {base_url}")
        print("Please check if the service is running and the host/port are correct")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ Request to Ollama service timed out")
        return False
    except Exception as e:
        print(f"❌ An error occurred: {str(e)}")
        return False

def test_generation(host="0.0.0.0", port=9000):
    """
    Test the generation endpoint with a simple prompt
    """
    try:
        base_url = f"http://{host}:{port}"
        
        # Simple test prompt
        payload = {
            "model": "qwen3-coder:30b-a3b-q4_K_M",
            "prompt": "print hello world in python language",
            "stream": False
        }
        
        print("\n📝 Testing generation with prompt: 'print hello world in python language'")
        response = requests.post(f"{base_url}/api/generate", 
                                json=payload, 
                                headers={"Content-Type": "application/json"},
                                timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Generation test successful")
            print(f"🤖 Model response:\n{result.get('response', 'No response field found')}")
            return True
        else:
            print(f"❌ Generation test failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during generation test: {str(e)}")
        return False

def main():
    host = "0.0.0.0"
    port = 9000
    
    print("🔍 Verifying Ollama Service")
    print("=" * 30)
    
    # Verify service is running
    if not verify_ollama_service(host, port):
        sys.exit(1)
    
    # Test generation
    print("\n" + "=" * 30)
    print("🧪 Testing Generation")
    print("=" * 30)
    
    if not test_generation(host, port):
        print("⚠️  Generation test failed, but service may still be functional")
    
    print("\n" + "=" * 30)
    print("✅ Verification complete")

if __name__ == "__main__":
    main()